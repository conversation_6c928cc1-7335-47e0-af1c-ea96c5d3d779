import React, { createContext, useState, useContext, useEffect } from 'react';
import axios from 'axios';

interface User {
  id: number;
  username: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  login: (username: string, password: string, totpCode?: string) => Promise<{ success: boolean; userRole?: string; error?: string; requires_2fa?: boolean }>;
  register: (username: string, email: string, password: string, fullName?: string) => Promise<boolean>;
  logout: () => void;
  checkAuthStatus: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Check authentication status on app load
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await axios.get('/auth/status');
      if (response.data.success && response.data.user) {
        setUser(response.data.user);
      } else {
        setUser(null);
        // Clear any stored auth data
        localStorage.removeItem('user');
        localStorage.removeItem('isAuthenticated');
      }
    } catch (error) {
      console.log('Auth check failed:', error);
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('isAuthenticated');
    } finally {
      setLoading(false);
    }
  };

  const login = async (username: string, password: string, totpCode?: string) => {
    try {
      console.log('🔐 Attempting login for:', username);
      
      const response = await axios.post('/admin/login', {
        username,
        password,
        totp_code: totpCode,
        turnstile_token: 'dev-bypass' // For development
      });

      console.log('📥 Login response:', response.data);

      if (response.data.success) {
        // Create user object with the role from response
        const userData = {
          id: 1, // You might want to get this from the response
          username: username,
          role: response.data.role
        };

        setUser(userData);
        
        // Store in localStorage for persistence
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');

        console.log('✅ Login successful, user set:', userData);

        return {
          success: true,
          userRole: response.data.role // Map 'role' to 'userRole' for frontend
        };
      } else {
        console.log('❌ Login failed:', response.data.message);
        return {
          success: false,
          error: response.data.message || 'Login failed',
          requires_2fa: response.data.requires_2fa
        };
      }
    } catch (error: any) {
      console.error('🚨 Login error:', error);
      
      let errorMessage = 'Login failed';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        error: errorMessage
      };
    }
  };

  const register = async (username: string, email: string, password: string, fullName?: string) => {
    try {
      const response = await axios.post('/customer/register', {
        username,
        email,
        password,
        fullName,
        turnstile_token: 'dev-bypass' // For development
      });

      return response.data.success;
    } catch (error: any) {
      console.error('Registration error:', error);
      throw new Error(error.response?.data?.message || 'Registration failed');
    }
  };

  const logout = async () => {
    try {
      // Determine logout endpoint based on user role
      const logoutEndpoint = user?.role === 'admin' || user?.role === 'super_admin' 
        ? '/admin/logout' 
        : '/customer/logout';
      
      await axios.get(logoutEndpoint);
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Clear state and localStorage regardless of API call success
      setUser(null);
      localStorage.removeItem('user');
      localStorage.removeItem('isAuthenticated');
      
      // Redirect to login page
      window.location.href = '/admin/login';
    }
  };

  const value = {
    user,
    login,
    register,
    logout,
    checkAuthStatus,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};