import React, { useState, useEffect } from 'react';
import { X, Eye, EyeOff, Lock, User } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { useNavigate } from 'react-router-dom';
import logoSvg from '../assets/logo.svg';

interface LoginPopupProps {
  isOpen: boolean;
  onClose: () => void;
}

const LoginPopup: React.FC<LoginPopupProps> = ({ isOpen, onClose }) => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  const [loginError, setLoginError] = useState('');
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const { login, user } = useAuth();
  const navigate = useNavigate();

  // Close on escape key
  useEffect(() => {
    const handleEsc = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
    };

    if (isOpen) {
      window.addEventListener('keydown', handleEsc);
    }

    return () => {
      window.removeEventListener('keydown', handleEsc);
    };
  }, [isOpen, onClose]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isOpen]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);

  // Handle login form submission
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    setIsLoggingIn(true);
    
    console.log('🔐 Login attempt with:', { username, password: '***' });

    // Basic validation
    if (!username || !password) {
      setLoginError('Please enter both username and password');
      setIsLoggingIn(false);
      return;
    }

    try {
      // Use the login function from AuthContext
      const result = await login(username, password);
      
      console.log('📥 Login result:', result);

      if (result.success) {
        console.log('✅ Login successful, user role:', result.userRole);
        
        // Close the popup first
        onClose();
        
        // Small delay to ensure popup closes before navigation
        setTimeout(() => {
          // Redirect based on user role
          if (result.userRole === 'admin' || result.userRole === 'super_admin' || result.userRole === 'moderator') {
            console.log('🔀 Redirecting admin to admin dashboard');
            navigate('/Me');
          } else {
            console.log('🔀 Redirecting customer to customer dashboard');
            navigate('/dashboard');
          }
        }, 100);
        
      } else {
        console.log('❌ Login failed:', result.error);
        
        if (result.requires_2fa) {
          setLoginError('2FA code required. Please contact support for assistance.');
        } else {
          setLoginError(result.error || 'Login failed');
        }
      }
    } catch (error: any) {
      console.error('🚨 Login error:', error);
      
      // Show more detailed error message if available
      if (error.message) {
        setLoginError(`Login failed: ${error.message}`);
      } else {
        setLoginError('Invalid username or password');
      }
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setUsername('');
    setPassword('');
    setLoginError('');
    setIsLoggingIn(false);
  };

  // Handle Discord login
  const handleDiscordLogin = () => {
    // Redirect to Discord OAuth
    const discordOAuthUrl = "https://discord.com/oauth2/authorize?client_id=1369841012528119819&response_type=code&redirect_uri=http%3A%2F%2Flocalhost%3A5173%2Fauth%2Fdiscord&scope=identify+email+guilds.join+guilds";

    // Save current state to localStorage if needed
    localStorage.setItem('loginRedirect', 'true');

    // Redirect to Discord OAuth
    window.location.href = discordOAuthUrl;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop with blur */}
      <div
        className="absolute inset-0 bg-black/70 backdrop-blur-md"
        onClick={onClose}
      />

      {/* Login/License panel */}
      <div className="relative w-full max-w-md bg-[#181818] rounded-lg border border-[#232323] shadow-2xl z-10 overflow-hidden">
        {/* Header with logo and title */}
        <div className="bg-[#181818] p-4 flex items-center justify-between">
          <img src={logoSvg} alt="Logo" className="w-8 h-8" />
          <h2 className="text-xl font-semibold text-white absolute left-1/2 transform -translate-x-1/2">
            Sign in
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
            disabled={isLoggingIn}
          >
            <X size={20} />
          </button>
        </div>

        {/* Login form */}
        <div className="p-6">
          <form onSubmit={handleLogin}>
            <div className="space-y-5">
              {/* Username field */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Username</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <User size={16} />
                  </div>
                  <input
                    type="text"
                    className="w-full bg-[#131313] border border-[#232323] text-white rounded-md py-2 pl-10 pr-3 focus:outline-none focus:border-brand-lime/50 transition-colors disabled:opacity-50"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={isLoggingIn}
                    required
                  />
                </div>
              </div>

              {/* Password field */}
              <div>
                <label className="block text-gray-400 text-sm mb-2">Password</label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-500">
                    <Lock size={16} />
                  </div>
                  <input
                    type={showPassword ? "text" : "password"}
                    className="w-full bg-[#131313] border border-[#232323] text-white rounded-md py-2 pl-10 pr-10 focus:outline-none focus:border-brand-lime/50 transition-colors disabled:opacity-50"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoggingIn}
                    required
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                    <button
                      type="button"
                      className="text-gray-500 hover:text-white transition-colors disabled:opacity-50"
                      onClick={() => setShowPassword(!showPassword)}
                      disabled={isLoggingIn}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>
              </div>

              {/* Login error message */}
              {loginError && (
                <div className="bg-red-500/10 border border-red-500/30 rounded-md p-3 text-sm text-red-400">
                  {loginError}
                </div>
              )}

              {/* Remember me and forgot password */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="remember-me"
                    className="w-4 h-4 rounded bg-[#131313] border-[#232323] focus:ring-brand-lime/50 text-brand-lime disabled:opacity-50"
                    checked={rememberMe}
                    onChange={() => setRememberMe(!rememberMe)}
                    disabled={isLoggingIn}
                  />
                  <label htmlFor="remember-me" className="ml-2 text-sm text-gray-400">
                    Remember me
                  </label>
                </div>
                <a href="#" className="text-sm text-brand-lime hover:underline">
                  Forgot password?
                </a>
              </div>

              {/* Login button */}
              <button
                type="submit"
                disabled={isLoggingIn}
                className="w-full bg-brand-lime text-[#131313] font-medium py-2 px-4 rounded-md hover:bg-brand-lime/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isLoggingIn ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-[#131313] mr-2"></div>
                    Signing in...
                  </>
                ) : (
                  'Login'
                )}
              </button>

              {/* Registration link */}
              <p className="text-center text-sm text-gray-400">
                Don't have an account?{' '}
                <button
                  type="button"
                  onClick={() => {
                    onClose();
                    // We'll add a callback to open the registration popup in App.tsx
                    if (window.openRegisterPopup) {
                      window.openRegisterPopup();
                    }
                  }}
                  className="text-brand-lime hover:underline"
                  disabled={isLoggingIn}
                >
                  Sign up
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default LoginPopup;